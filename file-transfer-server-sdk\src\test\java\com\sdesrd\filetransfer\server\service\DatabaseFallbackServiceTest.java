package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;

import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.DataAccessException;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileMetadata;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.UlidUtils;

/**
 * DatabaseFallbackService 单元测试类
 * 
 * <p>测试数据库故障回退服务的核心功能，包括数据库健康检查、
 * 基于info.json的文件查找和回退模式下的文件服务。</p>
 * 
 */
class DatabaseFallbackServiceTest {
    
    /**
     * 临时目录，用于测试文件操作
     */
    @TempDir
    Path tempDir;
    
    /**
     * 被测试的服务实例
     */
    private DatabaseFallbackService fallbackService;
    
    /**
     * Mock的数据库映射器
     */
    @Mock
    private FileTransferRecordMapper transferRecordMapper;
    
    /**
     * Mock的配置属性
     */
    @Mock
    private FileTransferProperties properties;
    
    /**
     * Mock的用户配置
     */
    @Mock
    private UserConfig userConfig;
    
    /**
     * Mock的元数据服务
     */
    @Mock
    private FileMetadataService metadataService;
    
    /**
     * 测试用的存储路径
     */
    private String testStoragePath;
    
    /**
     * 测试用的文件ID（使用有效的ULID）
     */
    private String testFileId;
    
    /**
     * 测试用的用户名
     */
    private static final String TEST_USERNAME = "testuser";
    
    /**
     * 测试用的原始文件名
     */
    private static final String TEST_ORIGINAL_FILENAME = "测试文档.pdf";
    
    /**
     * 测试用的物理文件名
     */
    private static final String TEST_PHYSICAL_FILENAME = "d41d8cd98f00b204e9800998ecf8427e.pdf";
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 生成一个有效的ULID用于测试
        testFileId = UlidUtils.generateUlid();
        
        fallbackService = new DatabaseFallbackService();
        // 使用反射设置私有字段
        setPrivateField(fallbackService, "transferRecordMapper", transferRecordMapper);
        setPrivateField(fallbackService, "properties", properties);
        setPrivateField(fallbackService, "metadataService", metadataService);
        
        testStoragePath = tempDir.toString();
        
        // 设置Mock行为
        when(properties.getDefaultConfig()).thenReturn(userConfig);
        when(userConfig.getStoragePath()).thenReturn(testStoragePath);
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试文件
        try {
            File storageDir = new File(testStoragePath);
            if (storageDir.exists()) {
                deleteDirectory(storageDir);
            }
        } catch (Exception e) {
            // 忽略清理错误
        }
    }
    
    /**
     * 测试数据库健康检查 - 正常情况
     */
    @Test
    void testDatabaseHealthyNormal() {
        // Mock数据库查询成功
        when(transferRecordMapper.selectList(any())).thenReturn(java.util.Collections.emptyList());
        
        // 执行健康检查
        boolean healthy = fallbackService.isDatabaseHealthy();
        
        // 验证结果
        assertTrue(healthy);
        verify(transferRecordMapper, times(1)).selectList(any());
    }
    
    /**
     * 测试数据库健康检查 - 数据库异常
     */
    @Test
    void testDatabaseHealthyException() {
        // Mock数据库查询异常
        when(transferRecordMapper.selectList(any())).thenThrow(new DataAccessException("数据库连接失败") {});
        
        // 执行健康检查
        boolean healthy = fallbackService.isDatabaseHealthy();
        
        // 验证结果
        assertFalse(healthy);
        verify(transferRecordMapper, times(1)).selectList(any());
    }
    
    /**
     * 测试数据库健康检查 - 响应缓慢
     */
    @Test
    void testDatabaseHealthySlow() {
        // Mock数据库查询缓慢（超过阈值）
        when(transferRecordMapper.selectList(any())).thenAnswer(invocation -> {
            Thread.sleep(6000); // 超过5秒阈值
            return java.util.Collections.emptyList();
        });
        
        // 执行健康检查
        boolean healthy = fallbackService.isDatabaseHealthy();
        
        // 验证结果
        assertFalse(healthy);
    }
    
    /**
     * 测试强制刷新数据库健康状态
     */
    @Test
    void testRefreshDatabaseHealth() {
        // Mock数据库查询成功
        when(transferRecordMapper.selectList(any())).thenReturn(java.util.Collections.emptyList());
        
        // 执行强制刷新
        boolean healthy = fallbackService.refreshDatabaseHealth();
        
        // 验证结果
        assertTrue(healthy);
        verify(transferRecordMapper, times(1)).selectList(any());
    }
    
    /**
     * 测试基于fileId查找文件（回退模式）- 使用元数据文件
     */
    @Test
    void testFindFileByIdFallbackWithMetadata() throws IOException {
        // 创建测试的元数据
        FileMetadata metadata = createTestMetadata();
        when(metadataService.readMetadata(testFileId, testStoragePath)).thenReturn(metadata);
        
        // 执行查找
        FileInfo fileInfo = fallbackService.findFileByIdFallback(testFileId, TEST_USERNAME);
        
        // 验证结果
        assertNotNull(fileInfo);
        assertEquals(testFileId, fileInfo.getFileId());
        assertEquals(TEST_ORIGINAL_FILENAME, fileInfo.getFileName());
        assertEquals(1024000L, fileInfo.getFileSize());
        
        verify(metadataService, times(1)).readMetadata(testFileId, testStoragePath);
    }
    
    /**
     * 测试基于fileId查找文件（回退模式）- 使用物理文件
     */
    @Test
    void testFindFileByIdFallbackWithPhysicalFile() throws IOException {
        // Mock没有元数据文件
        when(metadataService.readMetadata(testFileId, testStoragePath)).thenReturn(null);
        
        // 从ULID提取年月信息，创建正确的目录结构
        String yearMonth = UlidUtils.extractYearMonth(testFileId);
        assertNotNull(yearMonth, "应该能从ULID提取年月信息");
        
        Path fileIdDir = Paths.get(testStoragePath, yearMonth, testFileId);
        Files.createDirectories(fileIdDir);
        
        Path physicalFile = fileIdDir.resolve(TEST_PHYSICAL_FILENAME);
        Files.write(physicalFile, "测试文件内容".getBytes("UTF-8"));
        
        // 执行查找
        FileInfo fileInfo = fallbackService.findFileByIdFallback(testFileId, TEST_USERNAME);
        
        // 验证结果
        assertNotNull(fileInfo);
        assertEquals(testFileId, fileInfo.getFileId());
        assertEquals(TEST_PHYSICAL_FILENAME, fileInfo.getFileName());
        assertTrue(fileInfo.getFileSize() > 0);
        
        verify(metadataService, times(1)).readMetadata(testFileId, testStoragePath);
    }
    
    /**
     * 测试基于fileId查找文件（回退模式）- 文件不存在
     */
    @Test
    void testFindFileByIdFallbackNotFound() {
        // Mock没有元数据文件
        when(metadataService.readMetadata(testFileId, testStoragePath)).thenReturn(null);
        
        // 执行查找（没有创建物理文件）
        FileInfo fileInfo = fallbackService.findFileByIdFallback(testFileId, TEST_USERNAME);
        
        // 验证结果
        assertNull(fileInfo);
        
        verify(metadataService, times(1)).readMetadata(testFileId, testStoragePath);
    }
    
    /**
     * 测试无效参数处理
     */
    @Test
    void testInvalidParameters() {
        // 测试空fileId
        FileInfo result1 = fallbackService.findFileByIdFallback("", TEST_USERNAME);
        assertNull(result1);
        
        FileInfo result2 = fallbackService.findFileByIdFallback(null, TEST_USERNAME);
        assertNull(result2);
        
        // 验证没有调用metadataService
        verify(metadataService, never()).readMetadata(any(), any());
    }
    
    /**
     * 测试元数据文件损坏的情况
     */
    @Test
    void testCorruptedMetadata() {
        // Mock读取元数据时抛出异常
        when(metadataService.readMetadata(testFileId, testStoragePath))
            .thenThrow(new RuntimeException("元数据文件损坏"));
        
        // 执行查找
        FileInfo fileInfo = fallbackService.findFileByIdFallback(testFileId, TEST_USERNAME);
        
        // 验证结果（应该回退到null，因为没有物理文件）
        assertNull(fileInfo);
        
        verify(metadataService, times(1)).readMetadata(testFileId, testStoragePath);
    }
    
    /**
     * 测试不完整的元数据（状态不是完成）
     */
    @Test
    void testIncompleteMetadata() throws IOException {
        // 创建不完整的元数据（状态为传输中）
        FileMetadata incompleteMetadata = createTestMetadata();
        incompleteMetadata.setStatus(1); // 传输中状态
        
        when(metadataService.readMetadata(testFileId, testStoragePath)).thenReturn(incompleteMetadata);
        
        // 从ULID提取年月信息，创建正确的目录结构
        String yearMonth = UlidUtils.extractYearMonth(testFileId);
        assertNotNull(yearMonth, "应该能从ULID提取年月信息");
        
        Path fileIdDir = Paths.get(testStoragePath, yearMonth, testFileId);
        Files.createDirectories(fileIdDir);
        
        Path physicalFile = fileIdDir.resolve(TEST_PHYSICAL_FILENAME);
        Files.write(physicalFile, "测试文件内容".getBytes("UTF-8"));
        
        // 执行查找
        FileInfo fileInfo = fallbackService.findFileByIdFallback(testFileId, TEST_USERNAME);
        
        // 验证结果（应该使用物理文件信息）
        assertNotNull(fileInfo);
        assertEquals(TEST_PHYSICAL_FILENAME, fileInfo.getFileName()); // 使用物理文件名，不是原始文件名
        
        verify(metadataService, times(1)).readMetadata(testFileId, testStoragePath);
    }
    
    /**
     * 创建测试用的文件元数据
     * 
     * @return 测试元数据
     */
    private FileMetadata createTestMetadata() {
        FileMetadata metadata = new FileMetadata();
        metadata.setVersion("2.0.0");
        metadata.setFileId(testFileId);
        metadata.setOriginalFileName(TEST_ORIGINAL_FILENAME);
        metadata.setPhysicalFileName(TEST_PHYSICAL_FILENAME);
        metadata.setFileSize(1024000L);
        metadata.setFileMd5("d41d8cd98f00b204e9800998ecf8427e");
        metadata.setFileExtension("pdf");
        metadata.setFileType("application/pdf");
        metadata.setUploadTime(Instant.now().toString());
        metadata.setLastModified(Instant.now().toString());
        metadata.setStatus(2); // 传输完成
        metadata.setClientIp("127.0.0.1");
        metadata.setRelativePath("202412/" + testFileId + "/" + TEST_PHYSICAL_FILENAME);
        metadata.setTransferId("test-transfer-id");
        metadata.setTotalChunks(10);
        metadata.setCompletedChunks(10);
        metadata.setCreateTime(Instant.now().toString());
        
        return metadata;
    }
    
    /**
     * 使用反射设置私有字段
     * 
     * @param target 目标对象
     * @param fieldName 字段名
     * @param value 字段值
     */
    private void setPrivateField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("设置私有字段失败: " + fieldName, e);
        }
    }
    
    /**
     * 递归删除目录及其内容
     * 
     * @param directory 要删除的目录
     */
    private void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
} 