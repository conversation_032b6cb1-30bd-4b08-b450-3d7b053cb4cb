@echo off
REM ================================================================================
REM Simple File Transfer SDK Build Script (No Functions)
REM ================================================================================

setlocal enabledelayedexpansion

echo ========================================================
echo     Simple File Transfer SDK Build Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================

REM Parse command line arguments
set "execution_mode=build-test"
set "server_management=true"
if "%~1"=="build" (
    set "execution_mode=build"
    set "server_management=false"
)
if "%~1"=="build-test" (
    set "execution_mode=build-test"
)
if "%~1"=="--help" (
    echo Usage: %~nx0 [build^|build-test^|--help]
    echo   build             - Build only
    echo   build-test        - Build and test [default]
    echo   --help            - Show this help
    exit /b 0
)

echo [INFO] %date% %time% - Execution mode: %execution_mode%
if "%server_management%"=="true" (
    echo [INFO] %date% %time% - Server management: enabled
) else (
    echo [INFO] %date% %time% - Server management: disabled
)

REM Step 1: Setup Java environment
echo [STEP 1] %date% %time% - Setting up Java environment
echo ========================================

if exist "scripts\set-java-env-en.bat" (
    echo [INFO] %date% %time% - Calling Java environment setup script
    call "scripts\set-java-env-en.bat"
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Java environment setup script failed
        exit /b 1
    )
    echo [INFO] %date% %time% - Java environment setup script executed successfully
) else (
    echo [WARNING] %date% %time% - Java environment setup script not found, using system default Java
)

where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %date% %time% - Java Runtime not installed or not in PATH
    exit /b 1
)

echo [SUCCESS] %date% %time% - Java environment setup completed

REM Step 2: Check Maven environment
echo [STEP 2] %date% %time% - Checking Maven environment
echo ========================================

where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %date% %time% - Apache Maven not installed or not in PATH
    exit /b 1
)

echo [INFO] %date% %time% - Maven found in PATH
echo [SUCCESS] %date% %time% - Maven environment check completed

REM Step 3: Validate project structure
echo [STEP 3] %date% %time% - Validating project structure
echo ========================================

if not exist "pom.xml" (
    echo [ERROR] %date% %time% - Root pom.xml file does not exist
    exit /b 1
)
echo [INFO] %date% %time% - Root pom.xml file exists
echo [SUCCESS] %date% %time% - Project structure validation completed

REM ==================== Main Execution ====================
:main_execution

REM Step 4: Clean environment
echo [STEP 4] %date% %time% - Cleaning build environment
echo ========================================

echo [INFO] %date% %time% - Cleaning Maven build cache...
echo [DEBUG] About to execute: call mvn clean
call mvn clean
echo [DEBUG] Maven clean completed with errorlevel: !errorlevel!
if errorlevel 1 (
    echo [WARNING] %date% %time% - Maven clean failed, continuing anyway
)
echo [SUCCESS] %date% %time% - Environment cleanup completed
echo [DEBUG] About to proceed to Step 5

REM Step 5: Compile project
echo [STEP 5] %date% %time% - Compiling project
echo ========================================

echo [INFO] %date% %time% - Starting compilation of entire project...
echo [INFO] %date% %time% - Compile command: mvn compile -T 1C

call mvn compile -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] %date% %time% - Project compilation failed
    exit /b 1
)
echo [SUCCESS] %date% %time% - Project compilation successful

REM Step 6: Install project
echo [STEP 6] %date% %time% - Installing project to local Maven repository
echo ========================================

echo [INFO] %date% %time% - Starting installation of project to local Maven repository...
echo [INFO] %date% %time% - Install command: mvn install -DskipTests -T 1C

call mvn install -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] %date% %time% - Project installation failed
    exit /b 1
)
echo [SUCCESS] %date% %time% - Project installation successful

REM Steps 7-9: Test and package (only in build-test mode)
if "%execution_mode%"=="build-test" (
    REM Step 7: Start test server (if server management enabled)
    if "%server_management%"=="true" (
        echo [STEP 7] %date% %time% - Starting test server
        echo ========================================

        call :start_test_server
        if errorlevel 1 (
            echo [ERROR] %date% %time% - Failed to start test server
            exit /b 1
        )
    )

    REM Step 8: Run unit tests
    echo [STEP 8] %date% %time% - Running unit tests
    echo ========================================

    echo [INFO] %date% %time% - Starting unit tests...
    echo [INFO] %date% %time% - Test command: mvn test -T 1C

    call mvn test -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
    set "test_result=!errorlevel!"

    REM Step 9: Run end-to-end/integration tests (if any)
    echo [STEP 9] %date% %time% - Running integration/end-to-end tests (if any)
    echo ========================================
    REM (可根据需要扩展端到端测试命令)
    REM 这里只做占位，实际端到端测试可用类似 call mvn verify -pl <module> -Dtest=... 实现

    REM Step 10: Run client demo test
    echo [STEP 10] %date% %time% - Running client demo test
    echo ========================================
    call :run_client_demo_test
    if errorlevel 1 (
        echo [WARNING] %date% %time% - Client demo test failed or timed out
    ) else (
        echo [SUCCESS] %date% %time% - Client demo test completed successfully
    )

    REM Stop test server (if server management enabled)
    if "%server_management%"=="true" (
        echo [INFO] %date% %time% - Stopping test server...
        call :stop_test_server
    )

    REM Check test results
    if !test_result! neq 0 (
        echo [ERROR] %date% %time% - Unit tests failed
        exit /b 1
    )
    echo [SUCCESS] %date% %time% - Unit tests completed successfully
    
    REM Step 11: Package project
    echo [STEP 11] %date% %time% - Packaging project
    echo ========================================

    echo [INFO] %date% %time% - Starting project packaging...
    echo [INFO] %date% %time% - Package command: mvn package -DskipTests -T 1C

    call mvn package -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Project packaging failed
        exit /b 1
    )
    echo [SUCCESS] %date% %time% - Project packaging successful

    REM Step 12: Output report and logs
    echo [STEP 12] %date% %time% - Outputting report and logs
    echo ========================================
    REM 简单输出 surefire-reports 统计和日志路径
    for %%M in (file-transfer-server-sdk file-transfer-client-sdk) do (
        if exist "%%M\target\surefire-reports" (
            echo [INFO] %%M test summary:
            findstr /C:"<testsuite" "%%M\target\surefire-reports\TEST-*.xml"
        )
    )
    echo [INFO] Logs can be found in the logs directory if generated.
)

REM Final result
if "%execution_mode%"=="build" (
    echo [SUCCESS] %date% %time% - Build completed successfully
) else (
    if "%server_management%"=="true" (
        echo [SUCCESS] %date% %time% - Build, test, and server management completed successfully
    ) else (
        echo [SUCCESS] %date% %time% - Build and test completed successfully
    )
)

echo ========================================================
echo     Build script completed successfully!
echo     Time: %date% %time%
echo ========================================================

exit /b 0

REM ==================== Server Management Functions ====================

REM Check if port is available
:check_port_available
set "port=%~1"
netstat -an | findstr ":%port%" >nul 2>&1
if not errorlevel 1 (
    echo [ERROR] %date% %time% - Port %port% is already in use
    exit /b 1
)
echo [INFO] %date% %time% - Port %port% is available
exit /b 0

REM Start test server
:start_test_server
echo [INFO] %date% %time% - Starting test server...

REM Check if server standalone module exists
if not exist "file-transfer-server-standalone" (
    echo [ERROR] %date% %time% - Server standalone module not found
    exit /b 1
)

REM Check if server script exists
if not exist "file-transfer-server-standalone\start-server-en.bat" (
    echo [ERROR] %date% %time% - Server start script not found
    exit /b 1
)

REM Check if port 49011 is available
call :check_port_available 49011
if errorlevel 1 exit /b 1

REM Ensure server JAR is built
if not exist "file-transfer-server-standalone\target\file-transfer-server-standalone-1.0.0.jar" (
    echo [INFO] %date% %time% - Building server standalone module...
    call mvn package -pl file-transfer-server-standalone -DskipTests -T 1C
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Failed to build server standalone module
        exit /b 1
    )
)

REM Start server in background
echo [INFO] %date% %time% - Starting server on port 49011...
cd file-transfer-server-standalone
call start-server-en.bat start --port 49011 --background
if errorlevel 1 (
    cd ..
    echo [ERROR] %date% %time% - Failed to start test server
    exit /b 1
)
cd ..

REM Wait for server to be ready
echo [INFO] %date% %time% - Waiting for server to be ready...
timeout /t 10 /nobreak >nul

REM Verify server is running
call :check_server_running 49011
if errorlevel 1 (
    echo [ERROR] %date% %time% - Server failed to start properly
    exit /b 1
)

echo [SUCCESS] %date% %time% - Test server started successfully
exit /b 0

REM Check if server is running
:check_server_running
set "port=%~1"
netstat -an | findstr ":%port%" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %date% %time% - Server is not running on port %port%
    exit /b 1
)
echo [INFO] %date% %time% - Server is running on port %port%
exit /b 0

REM Stop test server
:stop_test_server
echo [INFO] %date% %time% - Stopping test server...

REM Check if server script exists
if not exist "file-transfer-server-standalone\start-server-en.bat" (
    echo [WARNING] %date% %time% - Server start script not found, trying alternative stop method
    goto :stop_server_alternative
)

REM Use server script to stop
cd file-transfer-server-standalone
call start-server-en.bat stop
cd ..
echo [SUCCESS] %date% %time% - Test server stopped
exit /b 0

:stop_server_alternative
echo [INFO] %date% %time% - Using alternative method to stop server...
taskkill /f /im java.exe >nul 2>&1
echo [SUCCESS] %date% %time% - Server processes stopped
exit /b 0

REM Run client demo test
:run_client_demo_test
REM 检查 client demo 模块是否存在
if not exist "file-transfer-client-demo" (
    echo [WARNING] %date% %time% - Client demo module not found, skipping demo test
    exit /b 1
)
REM 执行 client demo 程序
REM 这里假设 mainClass 为 com.sdesrd.filetransfer.demo.FileTransferClientDemo
REM 并传递必要参数（如有需要可调整）
set "DEMO_SERVER_HOST=localhost"
set "DEMO_SERVER_PORT=49011"
set "DEMO_USER_NAME=demo"
set "DEMO_USER_SECRET=demo-secret-key-2024"
set "DEMO_UPLOAD_DIR=demo-files/upload"
set "DEMO_DOWNLOAD_DIR=demo-files/download"

call mvn exec:java -pl file-transfer-client-demo -Dexec.mainClass="com.sdesrd.filetransfer.demo.FileTransferClientDemo" -Ddemo.server.host=%DEMO_SERVER_HOST% -Ddemo.server.port=%DEMO_SERVER_PORT% -Ddemo.user.name=%DEMO_USER_NAME% -Ddemo.user.secret=%DEMO_USER_SECRET% -Ddemo.upload.dir=%DEMO_UPLOAD_DIR% -Ddemo.download.dir=%DEMO_DOWNLOAD_DIR% -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [WARNING] %date% %time% - Client demo execution failed or timed out
    exit /b 1
)
REM 清理 demo-files 目录
if exist "demo-files" rmdir /s /q "demo-files"
exit /b 0
