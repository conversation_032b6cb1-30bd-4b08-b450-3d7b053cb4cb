# 文件传输SDK - frp风格的用户认证配置示例

# ===== 服务端配置 (application.yml) =====
file:
  transfer:
    server:
      enabled: true
      
      # 数据库配置
      database-path: ./data/file-transfer/database.db
      
      # 全局默认配置（兜底配置）
      default:
        storage-path: ./data/file-transfer/files
        upload-rate-limit: 5242880      # 5MB/s
        download-rate-limit: 5242880
        default-chunk-size: 1048576     # 1MB
        max-file-size: 52428800         # 50MB
        max-in-memory-size: 10485760    # 10MB
        fast-upload-enabled: true
        rate-limit-enabled: true
      
      # 多用户配置
      users:
        # 用户xxx - 基础用户
        xxx:
          secret-key: "337c7dc2-30fe-4603"
          storage-path: ./data/users/xxx/files
          upload-rate-limit: 1048576    # 1MB/s
          download-rate-limit: 1048576
          default-chunk-size: 524288    # 512KB
          max-file-size: 10485760       # 10MB
          max-in-memory-size: 5242880   # 5MB
          fast-upload-enabled: true
          rate-limit-enabled: true
          
        # 用户yyy - VIP用户
        yyy:
          secret-key: "337c7dc2-30fe-4602"
          storage-path: ./data/users/yyy/files
          upload-rate-limit: 10485760   # 10MB/s
          download-rate-limit: 10485760
          default-chunk-size: 2097152   # 2MB
          max-file-size: 104857600      # 100MB
          max-in-memory-size: 20971520  # 20MB
          fast-upload-enabled: true
          rate-limit-enabled: true
          
        # 用户admin - 管理员
        admin:
          secret-key: "admin-secret-key-2024"
          storage-path: ./data/admin/files
          upload-rate-limit: 52428800   # 50MB/s
          download-rate-limit: 52428800
          default-chunk-size: 4194304   # 4MB
          max-file-size: 1073741824     # 1GB
          max-in-memory-size: 52428800  # 50MB
          fast-upload-enabled: true
          rate-limit-enabled: false     # 管理员不限速

# ===== 客户端配置 (application.yml) =====
file:
  transfer:
    client:
      # 服务器连接配置
      server-addr: "your-domain.com"  # 或 IP地址
      server-port: 49011
      
      # 用户认证配置
      user: "xxx"                      # 用户名
      secret-key: "337c7dc2-30fe-4603" # 与服务端对应用户的密钥一致
      
      # 客户端传输配置
      chunk-size: 1048576              # 1MB (建议与服务端用户配置保持一致)
      max-concurrent-transfers: 2
      connect-timeout-seconds: 30
      read-timeout-seconds: 60
      retry-count: 3

---

# ===== Java代码使用示例 =====

# 服务端 - 无需额外代码，SDK自动处理认证

# 客户端 - 必须配置认证信息
# ```java
# ClientConfig config = new ClientConfig();
# config.getAuth().setServerAddr("your-domain.com");
# config.getAuth().setServerPort(49011);
# config.getAuth().setUser("xxx");
# config.getAuth().setSecretKey("337c7dc2-30fe-4603");
# config.setChunkSize(1024 * 1024); // 1MB
# 
# FileTransferClient client = new FileTransferClient(config);
# 
# // 注意：所有认证参数都是必填的，缺少任何一个都会抛出异常
# ```

# ===== 认证机制说明 =====
# 1. 认证方式：HMAC-SHA256签名
# 2. 认证头：X-File-Transfer-User (用户名)、X-File-Transfer-Auth (签名令牌)
# 3. 令牌格式：Base64(timestamp:signature)
# 4. 签名数据：username:timestamp
# 5. 令牌有效期：5分钟
# 6. secretKey：服务端和客户端必须一致

# ===== 安全建议 =====
# 1. secretKey使用强随机字符串，至少16位
# 2. 不同用户使用不同的secretKey
# 3. 定期更换secretKey
# 4. 生产环境使用HTTPS
# 5. 合理设置用户权限和限制 