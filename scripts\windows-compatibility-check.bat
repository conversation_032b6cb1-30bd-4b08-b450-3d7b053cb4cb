@echo off
REM ================================================================================
REM Windows兼容性检查脚本
REM 用途：验证Windows环境下的基本功能
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== 常量定义 ====================

set "SCRIPT_VERSION=1.0.0"
set "SCRIPT_NAME=Windows兼容性检查脚本"

REM ==================== 日志函数 ====================

:log_info
echo [INFO] %date% %time% - %~1
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     版本：%SCRIPT_VERSION%
echo     时间：%date% %time%
echo ========================================================
goto :eof

REM ==================== 检查函数 ====================

:check_command
set "command=%~1"
set "description=%~2"

where "%command%" >nul 2>&1
if errorlevel 1 (
    call :log_error "%description% 未安装或未在PATH中：%command%"
    exit /b 1
) else (
    call :log_success "%description% 可用：%command%"
)
exit /b 0

:check_java_environment
call :log_info "检查Java环境..."

REM 检查Java命令
call :check_command "java" "Java运行时"
if errorlevel 1 (
    call :log_error "Java环境检查失败"
    exit /b 1
)

REM 获取Java版本
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "Java版本：%java_version%"

REM 检查JAVA_HOME
if not "%JAVA_HOME%"=="" (
    call :log_success "JAVA_HOME已设置：%JAVA_HOME%"
    if exist "%JAVA_HOME%\bin\java.exe" (
        call :log_success "JAVA_HOME指向有效的Java安装"
    ) else (
        call :log_warning "JAVA_HOME指向的Java安装无效"
    )
) else (
    call :log_warning "JAVA_HOME未设置"
)

exit /b 0

:check_maven_environment
call :log_info "检查Maven环境..."

REM 检查Maven命令
call :check_command "mvn" "Apache Maven"
if errorlevel 1 (
    call :log_error "Maven环境检查失败"
    exit /b 1
)

REM 获取Maven版本
for /f "tokens=*" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
    set "maven_version=%%i"
    goto :maven_version_done
)
:maven_version_done
call :log_info "Maven版本：%maven_version%"

exit /b 0

:check_network_tools
call :log_info "检查网络工具..."

REM 检查netstat
call :check_command "netstat" "网络状态工具"

REM 检查tasklist
call :check_command "tasklist" "进程列表工具"

REM 检查taskkill
call :check_command "taskkill" "进程终止工具"

REM 检查curl（可选）
call :check_command "curl" "HTTP客户端工具"
if errorlevel 1 (
    call :log_warning "curl不可用，某些功能可能受限"
)

exit /b 0

:check_project_structure
call :log_info "检查项目结构..."

REM 检查根目录pom.xml
if exist "pom.xml" (
    call :log_success "根目录pom.xml存在"
) else (
    call :log_error "根目录pom.xml不存在"
    exit /b 1
)

REM 检查Windows批处理脚本
set "batch_scripts=scripts\set-java-env.bat file-transfer-server-standalone\start-server.bat build-and-test.bat"
for %%s in (%batch_scripts%) do (
    if exist "%%s" (
        call :log_success "Windows脚本存在：%%s"
    ) else (
        call :log_warning "Windows脚本不存在：%%s"
    )
)

REM 检查项目模块
set "modules=file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo file-transfer-server-standalone"
for %%m in (%modules%) do (
    if exist "%%m" (
        call :log_success "项目模块存在：%%m"
        if exist "%%m\pom.xml" (
            call :log_success "模块pom.xml存在：%%m\pom.xml"
        ) else (
            call :log_warning "模块pom.xml不存在：%%m\pom.xml"
        )
    ) else (
        call :log_warning "项目模块不存在：%%m"
    )
)

exit /b 0

:test_java_env_script
call :log_info "测试Java环境设置脚本..."

if exist "scripts\set-java-env.bat" (
    call :log_info "执行Java环境设置脚本..."
    call "scripts\set-java-env.bat" >nul 2>&1
    if not errorlevel 1 (
        call :log_success "Java环境设置脚本执行成功"
    ) else (
        call :log_warning "Java环境设置脚本执行失败"
    )
) else (
    call :log_error "Java环境设置脚本不存在"
    exit /b 1
)

exit /b 0

:test_path_handling
call :log_info "测试路径处理..."

REM 创建测试目录
set "test_dir=test-windows-compat"
if exist "%test_dir%" (
    rmdir /s /q "%test_dir%" 2>nul
)
mkdir "%test_dir%" 2>nul

if exist "%test_dir%" (
    call :log_success "目录创建成功：%test_dir%"
    
    REM 创建测试文件
    echo Windows兼容性测试 > "%test_dir%\test-file.txt"
    if exist "%test_dir%\test-file.txt" (
        call :log_success "文件创建成功：%test_dir%\test-file.txt"
    ) else (
        call :log_error "文件创建失败"
    )
    
    REM 清理测试目录
    rmdir /s /q "%test_dir%" 2>nul
    call :log_success "测试目录清理完成"
) else (
    call :log_error "目录创建失败：%test_dir%"
    exit /b 1
)

exit /b 0

REM ==================== 主程序 ====================

:main
call :show_header

call :log_info "开始Windows兼容性检查..."

REM 检查基本环境
call :check_java_environment
if errorlevel 1 (
    call :log_error "Java环境检查失败"
    exit /b 1
)

call :check_maven_environment
if errorlevel 1 (
    call :log_error "Maven环境检查失败"
    exit /b 1
)

call :check_network_tools
if errorlevel 1 (
    call :log_error "网络工具检查失败"
    exit /b 1
)

call :check_project_structure
if errorlevel 1 (
    call :log_error "项目结构检查失败"
    exit /b 1
)

call :test_java_env_script
if errorlevel 1 (
    call :log_error "Java环境脚本测试失败"
    exit /b 1
)

call :test_path_handling
if errorlevel 1 (
    call :log_error "路径处理测试失败"
    exit /b 1
)

echo.
call :log_success "Windows兼容性检查完成！"
call :log_info "所有基本功能检查通过，可以继续进行构建和测试"
echo.
call :log_info "下一步建议："
echo   1. 运行 build-and-test.bat build 进行构建测试
echo   2. 运行 file-transfer-server-standalone\start-server.bat start 测试服务器启动
echo   3. 查看 WINDOWS_COMPATIBILITY_GUIDE.md 了解详细使用说明
echo.

exit /b 0

REM 执行主函数
call :main %*
